
> Configure project :
Fabric Loom: 1.10.5

> Task :compileJava FAILED
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/network/town/ClaimHistorySynchronizer.java:158: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, CLAIM_HISTORY_UPDATE, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/network/chunk/ChunkDataSynchronizer.java:267: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, CHUNK_UPDATE, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/network/chunk/ChunkDataSynchronizer.java:356: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, CHUNK_BATCH_UPDATE, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:92: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                    NetworkManager.sendToPlayer(player, PLAYER_PERMISSION_UPDATE, buf, true);
                                  ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:102: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                        NetworkManager.sendToPlayer(subscriber, PLAYER_PERMISSION_UPDATE, buf, true);
                                      ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:164: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                NetworkManager.sendToPlayer(player, CHUNK_PERMISSION_UPDATE, buf, true);
                              ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:201: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                    NetworkManager.sendToPlayer(player, PERMISSION_UPDATE, buf, true);
                                  ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/invitation/TownInvitationSynchronizer.java:99: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, INVITATION_SYNC, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:105: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, IMAGE_VERSION_CHECK, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:248: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, IMAGE_DATA_RESPONSE, responseBuf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:280: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                NetworkManager.sendToPlayer(player, IMAGE_CHUNK_DATA, chunkBuf, true);
                              ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:289: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, IMAGE_TRANSFER_COMPLETE, completeBuf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/client/ClaimToolDataSynchronizer.java:80: warning: [unchecked] unchecked cast
                Map<String, Object> eventData = (Map<String, Object>) data;
                                                                      ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/notification/ServerNotificationSynchronizer.java:111: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, NOTIFICATION_SYNC, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:148: warning: [unchecked] unchecked cast
            return (Map<String, Object>) categoryPrefs;
                                         ^
  required: Map<String,Object>
  found:    Object
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:192: warning: [unchecked] unchecked conversion
            Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);
                                                           ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:325: warning: [unchecked] unchecked conversion
            Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
                                                      ^
  required: Map<String,Object>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:60: warning: [unchecked] unchecked conversion
            Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
                                                                   ^
  required: Map<String,Map<String,Object>>
  found:    Map
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:192: warning: [unchecked] unchecked cast
            return (T) value;
                       ^
  required: T
  found:    Object
  where T is a type-variable:
    T extends Object declared in method <T>getPreference(String,String,T)
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/phone/network/PhoneDataSynchronizer.java:129: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, PHONE_APP_POSITIONS_SYNC, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/phone/network/PhoneDataSynchronizer.java:180: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, PHONE_NOTIFICATION_SYNC, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
/home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/phone/network/PhoneDataSynchronizer.java:237: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
            NetworkManager.sendToPlayer(player, PHONE_SETTINGS_SYNC, buf, true);
                          ^
  required: ServerPlayerEntity,Identifier,PacketByteBuf
  found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
  reason: actual and formal argument lists differ in length
16 errors
6 warnings

[Incubating] Problems report is available at: file:///home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':compileJava'.
> Compilation failed; see the compiler output below.
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/network/town/ClaimHistorySynchronizer.java:158: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, CLAIM_HISTORY_UPDATE, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/network/chunk/ChunkDataSynchronizer.java:267: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, CHUNK_UPDATE, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/network/chunk/ChunkDataSynchronizer.java:356: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, CHUNK_BATCH_UPDATE, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:92: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                      NetworkManager.sendToPlayer(player, PLAYER_PERMISSION_UPDATE, buf, true);
                                    ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:102: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                          NetworkManager.sendToPlayer(subscriber, PLAYER_PERMISSION_UPDATE, buf, true);
                                        ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:164: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                  NetworkManager.sendToPlayer(player, CHUNK_PERMISSION_UPDATE, buf, true);
                                ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/permission/PermissionSynchronizer.java:201: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                      NetworkManager.sendToPlayer(player, PERMISSION_UPDATE, buf, true);
                                    ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/invitation/TownInvitationSynchronizer.java:99: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, INVITATION_SYNC, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:105: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, IMAGE_VERSION_CHECK, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:248: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, IMAGE_DATA_RESPONSE, responseBuf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:280: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
                  NetworkManager.sendToPlayer(player, IMAGE_CHUNK_DATA, chunkBuf, true);
                                ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/image/EnhancedImageSynchronizer.java:289: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, IMAGE_TRANSFER_COMPLETE, completeBuf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/town/client/ClaimToolDataSynchronizer.java:80: warning: [unchecked] unchecked cast
                  Map<String, Object> eventData = (Map<String, Object>) data;
                                                                        ^
    required: Map<String,Object>
    found:    Object
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/notification/ServerNotificationSynchronizer.java:111: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, NOTIFICATION_SYNC, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:148: warning: [unchecked] unchecked cast
              return (Map<String, Object>) categoryPrefs;
                                           ^
    required: Map<String,Object>
    found:    Object
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:192: warning: [unchecked] unchecked conversion
              Map<String, Object> preferences = gson.fromJson(jsonObject, Map.class);
                                                             ^
    required: Map<String,Object>
    found:    Map
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/ConfigSynchronizer.java:325: warning: [unchecked] unchecked conversion
              Map<String, Object> config = gson.fromJson(jsonValue, Map.class);
                                                        ^
    required: Map<String,Object>
    found:    Map
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:60: warning: [unchecked] unchecked conversion
              Map<String, Map<String, Object>> loaded = gson.fromJson(reader, Map.class);
                                                                     ^
    required: Map<String,Map<String,Object>>
    found:    Map
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/config/UserPreferencesManager.java:192: warning: [unchecked] unchecked cast
              return (T) value;
                         ^
    required: T
    found:    Object
    where T is a type-variable:
      T extends Object declared in method <T>getPreference(String,String,T)
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/phone/network/PhoneDataSynchronizer.java:129: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, PHONE_APP_POSITIONS_SYNC, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/phone/network/PhoneDataSynchronizer.java:180: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, PHONE_NOTIFICATION_SYNC, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  /home/<USER>/Documents/!servmc/!custommod/claim/pokecobbleclaim-template-1.20.1/src/main/java/com/pokecobble/phone/network/PhoneDataSynchronizer.java:237: error: method sendToPlayer in class NetworkManager cannot be applied to given types;
              NetworkManager.sendToPlayer(player, PHONE_SETTINGS_SYNC, buf, true);
                            ^
    required: ServerPlayerEntity,Identifier,PacketByteBuf
    found:    ServerPlayerEntity,Identifier,PacketByteBuf,boolean
    reason: actual and formal argument lists differ in length
  16 errors
  6 warnings

* Try:
> Check your code and dependencies to fix the compilation error(s)
> Run with --scan to get full insights.

BUILD FAILED in 2s
1 actionable task: 1 executed
